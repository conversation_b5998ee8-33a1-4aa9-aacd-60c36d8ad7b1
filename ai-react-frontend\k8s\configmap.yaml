apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-react-frontend-config
  namespace: ai-react-frontend-production
  labels:
    app: ai-react-frontend
    component: config
    environment: production
    app-type: react-frontend
data:
  # Application Configuration
<<<<<<< HEAD
  NODE_ENV: "dev"
  PORT: "3000"
  # React Frontend Configuration
  REACT_APP_API_URL: "http://ai-react-frontend-service:3000"
  REACT_APP_ENVIRONMENT: "dev"
=======
  NODE_ENV: "production"
  PORT: "80"
  # React Frontend Configuration
  REACT_APP_API_URL: "http://ai-react-frontend-service:80"
  REACT_APP_ENVIRONMENT: "production"
>>>>>>> 1b4277e5a5422d38142972c042e9907630a170e0
  REACT_APP_VERSION: "1.0.0"
  REACT_APP_TITLE: "AI React Frontend"
  PUBLIC_URL: "http://ai-react-frontend.production.local"
  GENERATE_SOURCEMAP: "false"
